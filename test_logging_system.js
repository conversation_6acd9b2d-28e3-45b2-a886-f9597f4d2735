// Test script to verify the updated MongoDB logging system
// This script tests Partner entity creation and updates to ensure
// related entities (IndividualDetail, CustomerDetail) are being logged

const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const baseUrl = 'https://localhost:7137';

function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 7137,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            rejectUnauthorized: false
        };

        if (data) {
            const jsonData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(jsonData);
        }

        const req = https.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsedData = responseData ? JSON.parse(responseData) : null;
                    resolve({
                        statusCode: res.statusCode,
                        data: parsedData,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        data: responseData,
                        headers: res.headers
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

async function testLoggingSystem() {
    console.log('=== Testing FastERP MongoDB Logging System ===\n');

    try {
        // Test 1: Create Partner with IndividualDetail
        console.log('Test 1: Creating Partner with IndividualDetail...');
        const createData = {
            individualDetail: {
                lastName: "測試",
                firstName: "用戶",
                birthDate: null
            }
        };

        const createResponse = await makeRequest('POST', '/api/Partner', createData);
        console.log(`Status: ${createResponse.statusCode}`);
        
        if (createResponse.statusCode === 200 || createResponse.statusCode === 201) {
            console.log('✓ Partner created successfully');
            const partnerId = createResponse.data?.partnerID;
            console.log(`Partner ID: ${partnerId}\n`);

            if (partnerId) {
                // Test 2: Update Partner with IndividualDetail and CustomerDetail
                console.log('Test 2: Updating Partner with IndividualDetail and CustomerDetail...');
                const updateData = {
                    partnerID: partnerId,
                    individualDetail: {
                        lastName: "測試",
                        firstName: "用戶",
                        identificationNumber: "A123456789",
                        birthDate: null,
                        partnerID: partnerId
                    },
                    customerDetail: {
                        customerCategoryID: "355fd89a-0201-4789-8698-1e0ae4eb270a",
                        settlementDay: 15,
                        partnerID: partnerId
                    }
                };

                const updateResponse = await makeRequest('PUT', '/api/Partner', updateData);
                console.log(`Status: ${updateResponse.statusCode}`);
                
                if (updateResponse.statusCode === 200) {
                    console.log('✓ Partner updated successfully\n');
                } else {
                    console.log('✗ Partner update failed');
                    console.log('Response:', updateResponse.data);
                }
            }
        } else {
            console.log('✗ Partner creation failed');
            console.log('Response:', createResponse.data);
        }

        console.log('=== Test completed ===');
        console.log('Please check the MongoDB logs to verify that:');
        console.log('1. Partner entity changes are logged');
        console.log('2. IndividualDetail entity changes are logged');
        console.log('3. CustomerDetail entity changes are logged');
        console.log('4. All related entity changes have proper before/after data');

    } catch (error) {
        console.error('Test failed with error:', error.message);
    }
}

// Run the test
testLoggingSystem();
