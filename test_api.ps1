# Test script to verify the updated MongoDB logging system
# This script tests Partner entity creation and updates

Write-Host "=== Testing FastERP MongoDB Logging System ===" -ForegroundColor Green

# Disable SSL certificate validation for localhost testing
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy

try {
    # Test 1: Create Partner with IndividualDetail
    Write-Host "`nTest 1: Creating Partner with IndividualDetail..." -ForegroundColor Yellow

    $createBody = @{
        individualDetail = @{
            lastName = "測試"
            firstName = "用戶"
            birthDate = $null
        }
    } | ConvertTo-Json -Depth 10

    $createResponse = Invoke-RestMethod -Uri "https://localhost:7137/api/Partner" -Method POST -ContentType "application/json" -Body $createBody
    
    Write-Host "✓ Partner created successfully" -ForegroundColor Green
    Write-Host "Partner ID: $($createResponse.partnerID)" -ForegroundColor Cyan
    
    $partnerId = $createResponse.partnerID
    
    if ($partnerId) {
        # Test 2: Update Partner with IndividualDetail and CustomerDetail
        Write-Host "`nTest 2: Updating Partner with IndividualDetail and CustomerDetail..." -ForegroundColor Yellow
        
        $updateBody = @{
            partnerID = $partnerId
            individualDetail = @{
                lastName = "測試"
                firstName = "用戶"
                identificationNumber = "A123456789"
                birthDate = $null
                partnerID = $partnerId
            }
            customerDetail = @{
                customerCategoryID = "355fd89a-0201-4789-8698-1e0ae4eb270a"
                settlementDay = 15
                partnerID = $partnerId
            }
        } | ConvertTo-Json -Depth 10
        
        $updateResponse = Invoke-RestMethod -Uri "https://localhost:7137/api/Partner" -Method PUT -ContentType "application/json" -Body $updateBody
        
        Write-Host "✓ Partner updated successfully" -ForegroundColor Green
    }
    
    Write-Host "`n=== Test completed ===" -ForegroundColor Green
    Write-Host "Please check the MongoDB logs to verify that:" -ForegroundColor Cyan
    Write-Host "1. Partner entity changes are logged" -ForegroundColor White
    Write-Host "2. IndividualDetail entity changes are logged" -ForegroundColor White
    Write-Host "3. CustomerDetail entity changes are logged" -ForegroundColor White
    Write-Host "4. All related entity changes have proper before/after data" -ForegroundColor White
    
} catch {
    Write-Host "✗ Test failed with error:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host $_.Exception.Response.StatusCode -ForegroundColor Red
}
