using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 增強的實體變更追蹤器
    /// 專門處理 FastERP 複雜實體結構，避免循環引用
    /// </summary>
    public class EnhancedEntityChangeTracker
    {
        private readonly ILogger<EnhancedEntityChangeTracker> _logger;
        private readonly HashSet<string> _excludedProperties;

        public EnhancedEntityChangeTracker(ILogger<EnhancedEntityChangeTracker> logger)
        {
            _logger = logger;
            
            // 排除導航屬性和複雜類型，避免循環引用
            _excludedProperties = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                // Partner 相關導航屬性
                "IndividualDetail", "EnterpriseDetail", "CustomerDetail", "SupplierDetail",
                "PartnerContacts", "Addresses", "Partner",
                
                // 通用導航屬性
                "CreatedByUser", "UpdatedByUser", "Navigation", "Reference", "Collection",
                
                // Entity Framework 內部屬性
                "EntityState", "Entity", "Metadata", "Properties", "Navigations", "References", "Collections"
            };
        }

        public List<EntityChangeRecord<T>> CaptureEntityChanges<T>(ChangeTracker changeTracker, string transactionId) where T : class
        {
            var changes = new List<EntityChangeRecord<T>>();

            try
            {
                var entries = changeTracker.Entries<T>()
                    .Where(e => e.State != EntityState.Unchanged)
                    .ToList();

                foreach (var entry in entries)
                {
                    var changeRecord = CreateChangeRecord(entry, transactionId);
                    if (changeRecord != null)
                    {
                        changes.Add(changeRecord);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "捕獲實體變更失敗");
            }

            return changes;
        }

        private EntityChangeRecord<T>? CreateChangeRecord<T>(EntityEntry<T> entry, string transactionId) where T : class
        {
            try
            {
                var entityType = entry.Entity.GetType();
                var entityId = ExtractEntityId(entry.Entity);

                var changeRecord = new EntityChangeRecord<T>
                {
                    EntityType = entityType.Name,
                    EntityId = entityId,
                    State = entry.State,
                    Timestamp = DateTime.UtcNow.AddHours(8), // 台灣時區
                    TransactionId = transactionId,
                    UserId = ExtractUserId(entry.Entity)
                };

                // 安全地提取屬性值
                ExtractPropertyValues(entry, changeRecord);

                return changeRecord;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "創建變更記錄失敗: {EntityType}", typeof(T).Name);
                return null;
            }
        }

        private void ExtractPropertyValues<T>(EntityEntry<T> entry, EntityChangeRecord<T> changeRecord) where T : class
        {
            try
            {
                var properties = entry.Properties
                    .Where(p => !_excludedProperties.Contains(p.Metadata.Name))
                    .Where(p => IsSimpleProperty(p.Metadata.ClrType))
                    .ToList();

                foreach (var property in properties)
                {
                    try
                    {
                        var propertyName = property.Metadata.Name;
                        
                        // 提取原始值（僅對修改和刪除操作）
                        if (entry.State == EntityState.Modified || entry.State == EntityState.Deleted)
                        {
                            var originalValue = property.OriginalValue;
                            changeRecord.OriginalValues[propertyName] = ConvertToSafeValue(originalValue);
                        }

                        // 提取當前值（僅對新增和修改操作）
                        if (entry.State == EntityState.Added || entry.State == EntityState.Modified)
                        {
                            var currentValue = property.CurrentValue;
                            changeRecord.CurrentValues[propertyName] = ConvertToSafeValue(currentValue);
                        }

                        // 檢查是否為變更的屬性
                        if (entry.State == EntityState.Modified && property.IsModified)
                        {
                            changeRecord.ChangedProperties.Add(propertyName);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "提取屬性值失敗: {PropertyName}", property.Metadata.Name);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取屬性值失敗");
            }
        }

        private bool IsSimpleProperty(Type type)
        {
            // 檢查是否為簡單類型，避免複雜物件和導航屬性
            var underlyingType = Nullable.GetUnderlyingType(type) ?? type;
            
            return underlyingType.IsPrimitive ||
                   underlyingType.IsEnum ||
                   underlyingType == typeof(string) ||
                   underlyingType == typeof(DateTime) ||
                   underlyingType == typeof(DateTimeOffset) ||
                   underlyingType == typeof(TimeSpan) ||
                   underlyingType == typeof(Guid) ||
                   underlyingType == typeof(decimal);
        }

        private object? ConvertToSafeValue(object? value)
        {
            if (value == null) return null;

            // 將 Guid 轉換為字串，避免序列化問題
            if (value is Guid guid)
                return guid.ToString();

            // 將 DateTime 轉換為 ISO 字串
            if (value is DateTime dateTime)
                return dateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

            // 將 DateTimeOffset 轉換為 ISO 字串
            if (value is DateTimeOffset dateTimeOffset)
                return dateTimeOffset.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

            return value;
        }

        private string ExtractEntityId(object entity)
        {
            try
            {
                // 嘗試多種可能的 ID 屬性名稱
                var idProperties = new[] { "Id", "ID", "PartnerID", "ItemID", "OrderID" };
                
                foreach (var idProp in idProperties)
                {
                    var property = entity.GetType().GetProperty(idProp);
                    if (property != null)
                    {
                        var value = property.GetValue(entity);
                        return value?.ToString() ?? "Unknown";
                    }
                }

                // 如果找不到標準 ID 屬性，使用 GetHashCode
                return entity.GetHashCode().ToString();
            }
            catch
            {
                return "Unknown";
            }
        }

        private string? ExtractUserId(object entity)
        {
            try
            {
                // 嘗試從實體中提取用戶 ID
                var userIdProperties = new[] { "UpdateUserId", "CreatedUserId", "UserId" };
                
                foreach (var userIdProp in userIdProperties)
                {
                    var property = entity.GetType().GetProperty(userIdProp);
                    if (property != null)
                    {
                        var value = property.GetValue(entity);
                        return value?.ToString();
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }
    }
}
