using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 日誌資料結構
    /// 專門處理實體變更和業務資料
    /// </summary>
    public class LogData
    {
        [BsonElement("operation")]
        public string Operation { get; set; } = string.Empty;
        
        [BsonElement("entityType")]
        public string EntityType { get; set; } = string.Empty;
        
        [BsonElement("entityId")]
        public string EntityId { get; set; } = string.Empty;
        
        [BsonElement("summary")]
        public string Summary { get; set; } = string.Empty;
        
        [BsonElement("beforeData")]
        public string? BeforeData { get; set; }
        
        [BsonElement("afterData")]
        public string? AfterData { get; set; }
        
        [BsonElement("changedFields")]
        public List<string> ChangedFields { get; set; } = new List<string>();
        
        [BsonElement("status")]
        public string Status { get; set; } = string.Empty;
        
        [BsonElement("additionalData")]
        public Dictionary<string, object>? AdditionalData { get; set; }
    }
}
