using FAST_ERP_Backend.Models.Common;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// 日誌資料處理器介面
    /// 負責安全序列化和資料格式化
    /// </summary>
    public interface ILogDataProcessor
    {
        Task<SimpleLogEntry> ProcessEntityChangeAsync<T>(EntityChangeRecord<T> changeRecord) where T : class;
        Task<SimpleLogEntry> ProcessLogEntryAsync(string message, object? data, string transactionId, string source);
        string SafeSerialize(object? obj);
    }
}
