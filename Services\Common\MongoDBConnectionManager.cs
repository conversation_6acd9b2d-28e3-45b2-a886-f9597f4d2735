using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using System;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// MongoDB 連接管理器實作
    /// 提供連接池管理、健康檢查和自動重連機制
    /// </summary>
    public class MongoDBConnectionManager : IMongoDBConnectionManager
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<MongoDBConnectionManager> _logger;
        private IMongoClient? _client;
        private IMongoDatabase? _database;
        private readonly object _lockObject = new object();
        private DateTime _lastHealthCheck = DateTime.MinValue;
        private bool _isHealthy = false;

        public MongoDBConnectionManager(IConfiguration configuration, ILogger<MongoDBConnectionManager> logger)
        {
            _configuration = configuration;
            _logger = logger;
            InitializeConnection();
        }

        private void InitializeConnection()
        {
            try
            {
                var connectionString = _configuration["MongoDB:ConnectionString"] 
                    ?? throw new InvalidOperationException("MongoDB連接字串未配置");
                var databaseName = _configuration["MongoDB:DatabaseName"] 
                    ?? throw new InvalidOperationException("MongoDB資料庫名稱未配置");

                var settings = MongoClientSettings.FromConnectionString(connectionString);
                settings.MaxConnectionPoolSize = 100;
                settings.MinConnectionPoolSize = 5;
                settings.MaxConnectionIdleTime = TimeSpan.FromMinutes(10);
                settings.ServerSelectionTimeout = TimeSpan.FromSeconds(30);

                _client = new MongoClient(settings);
                _database = _client.GetDatabase(databaseName);
                
                _logger.LogInformation("MongoDB連接管理器初始化成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MongoDB連接管理器初始化失敗");
                throw;
            }
        }

        public async Task<IMongoCollection<T>> GetCollectionAsync<T>(string collectionName)
        {
            if (_database == null)
            {
                lock (_lockObject)
                {
                    if (_database == null)
                    {
                        InitializeConnection();
                    }
                }
            }

            // 定期健康檢查
            if (DateTime.UtcNow - _lastHealthCheck > TimeSpan.FromMinutes(5))
            {
                await HealthCheckAsync();
            }

            return _database!.GetCollection<T>(collectionName);
        }

        public async Task<bool> HealthCheckAsync()
        {
            try
            {
                if (_database == null) return false;

                // 執行簡單的 ping 操作
                await _database.RunCommandAsync((Command<MongoDB.Bson.BsonDocument>)"{ping:1}");
                
                _isHealthy = true;
                _lastHealthCheck = DateTime.UtcNow;
                
                _logger.LogDebug("MongoDB健康檢查通過");
                return true;
            }
            catch (Exception ex)
            {
                _isHealthy = false;
                _logger.LogWarning(ex, "MongoDB健康檢查失敗");
                return false;
            }
        }

        public async Task<bool> ResetConnectionAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    _client = null;
                    _database = null;
                }

                InitializeConnection();
                return await HealthCheckAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MongoDB連接重置失敗");
                return false;
            }
        }

        public async Task<MongoDBConnectionStatus> GetConnectionStatusAsync()
        {
            var isConnected = await HealthCheckAsync();
            
            return new MongoDBConnectionStatus
            {
                IsConnected = isConnected,
                ConnectionString = _configuration["MongoDB:ConnectionString"] ?? "",
                DatabaseName = _configuration["MongoDB:DatabaseName"] ?? "",
                LastHealthCheck = _lastHealthCheck,
                ErrorMessage = isConnected ? null : "連接檢查失敗",
                ActiveConnections = isConnected ? 1 : 0
            };
        }
    }
}
