using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 日誌資料處理器實作
    /// 專門處理 FastERP 複雜實體結構的安全序列化
    /// </summary>
    public class LogDataProcessor : ILogDataProcessor
    {
        private readonly ILogger<LogDataProcessor> _logger;
        private readonly JsonSerializerOptions _safeJsonOptions;
        private readonly HashSet<string> _sensitiveFields;

        public LogDataProcessor(ILogger<LogDataProcessor> logger)
        {
            _logger = logger;
            
            // 配置安全的 JSON 序列化選項
            _safeJsonOptions = new JsonSerializerOptions
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                MaxDepth = 3 // 限制序列化深度，避免深層循環引用
            };

            // 定義敏感欄位清單
            _sensitiveFields = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "password", "pwd", "secret", "token", "key", "credential",
                "ssn", "idnumber", "creditcard", "bankaccount"
            };
        }

        public async Task<SimpleLogEntry> ProcessEntityChangeAsync<T>(EntityChangeRecord<T> changeRecord) where T : class
        {
            var entry = new SimpleLogEntry
            {
                Timestamp = changeRecord.Timestamp,
                Level = "Information",
                Message = $"實體變更: {changeRecord.EntityType}",
                Source = "EntityChangeTracker",
                TransactionId = changeRecord.TransactionId,
                UserId = changeRecord.UserId,
                Data = new LogData
                {
                    Operation = changeRecord.State.ToString(),
                    EntityType = changeRecord.EntityType,
                    EntityId = changeRecord.EntityId,
                    Summary = $"{changeRecord.EntityType} {changeRecord.State}",
                    Status = "Success"
                }
            };

            try
            {
                // 安全序列化 before/after 資料
                if (changeRecord.OriginalValues.Any())
                {
                    var safeOriginal = FilterSensitiveData(changeRecord.OriginalValues);
                    entry.Data.BeforeData = SafeSerialize(safeOriginal);
                }

                if (changeRecord.CurrentValues.Any())
                {
                    var safeCurrent = FilterSensitiveData(changeRecord.CurrentValues);
                    entry.Data.AfterData = SafeSerialize(safeCurrent);
                }

                entry.Data.ChangedFields = changeRecord.ChangedProperties.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "實體變更資料序列化失敗: {EntityType}", changeRecord.EntityType);
                entry.Data.Status = "SerializationError";
                entry.Data.AdditionalData = new Dictionary<string, object>
                {
                    ["serializationError"] = ex.Message,
                    ["changedFieldsCount"] = changeRecord.ChangedProperties.Count
                };
            }

            return await Task.FromResult(entry);
        }

        public async Task<SimpleLogEntry> ProcessLogEntryAsync(string message, object? data, string transactionId, string source)
        {
            var entry = new SimpleLogEntry
            {
                Message = message,
                Source = source,
                TransactionId = transactionId,
                Level = "Information",
                Data = new LogData
                {
                    Operation = "LogEntry",
                    Summary = message,
                    Status = "Success"
                }
            };

            if (data != null)
            {
                try
                {
                    var safeData = FilterSensitiveData(data);
                    entry.Data.AfterData = SafeSerialize(safeData);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "日誌資料序列化失敗");
                    entry.Data.Status = "SerializationError";
                    entry.Data.AdditionalData = new Dictionary<string, object>
                    {
                        ["serializationError"] = ex.Message
                    };
                }
            }

            return await Task.FromResult(entry);
        }

        public string SafeSerialize(object? obj)
        {
            if (obj == null) return "null";

            try
            {
                // 第一層：嘗試標準序列化
                return JsonSerializer.Serialize(obj, _safeJsonOptions);
            }
            catch (JsonException)
            {
                try
                {
                    // 第二層：轉換為安全的字典格式
                    var safeObj = ConvertToSafeDictionary(obj);
                    return JsonSerializer.Serialize(safeObj, _safeJsonOptions);
                }
                catch
                {
                    // 第三層：降級為基本字串表示
                    return obj.ToString() ?? "null";
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "物件序列化完全失敗");
                return $"[序列化失敗: {ex.Message}]";
            }
        }

        private Dictionary<string, object?> ConvertToSafeDictionary(object obj)
        {
            var result = new Dictionary<string, object?>();
            var type = obj.GetType();

            // 只處理基本屬性，避免導航屬性
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && IsSimpleType(p.PropertyType))
                .Take(20); // 限制屬性數量

            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(obj);
                    result[prop.Name] = value;
                }
                catch
                {
                    result[prop.Name] = "[無法讀取]";
                }
            }

            return result;
        }

        private bool IsSimpleType(Type type)
        {
            return type.IsPrimitive ||
                   type.IsEnum ||
                   type == typeof(string) ||
                   type == typeof(DateTime) ||
                   type == typeof(DateTimeOffset) ||
                   type == typeof(TimeSpan) ||
                   type == typeof(Guid) ||
                   type == typeof(decimal) ||
                   Nullable.GetUnderlyingType(type) != null;
        }

        private object FilterSensitiveData(object data)
        {
            if (data is Dictionary<string, object?> dict)
            {
                var filtered = new Dictionary<string, object?>();
                foreach (var kvp in dict)
                {
                    if (_sensitiveFields.Contains(kvp.Key))
                    {
                        filtered[kvp.Key] = "[已過濾]";
                    }
                    else
                    {
                        filtered[kvp.Key] = kvp.Value;
                    }
                }
                return filtered;
            }

            return data;
        }
    }
}
