using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 實體變更記錄
    /// 安全地捕獲實體變更資訊，避免循環引用
    /// </summary>
    /// <typeparam name="T">實體類型</typeparam>
    public class EntityChangeRecord<T> where T : class
    {
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public EntityState State { get; set; }
        public string? UserId { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object?> OriginalValues { get; set; } = new();
        public Dictionary<string, object?> CurrentValues { get; set; } = new();
        public List<string> ChangedProperties { get; set; } = new();
        public string TransactionId { get; set; } = string.Empty;
    }
}
