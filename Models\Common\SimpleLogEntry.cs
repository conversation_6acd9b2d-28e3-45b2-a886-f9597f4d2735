using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;

namespace FAST_ERP_Backend.Models.Common
{
    /// <summary>
    /// 簡化的 MongoDB 日誌條目
    /// 解決循環引用和複雜序列化問題
    /// </summary>
    public class SimpleLogEntry
    {
        [BsonId]
        public ObjectId Id { get; set; }
        
        [BsonElement("timestamp")]
        public DateTime Timestamp { get; set; }
        
        [BsonElement("level")]
        public string Level { get; set; } = string.Empty;
        
        [BsonElement("message")]
        public string Message { get; set; } = string.Empty;
        
        [BsonElement("source")]
        public string Source { get; set; } = string.Empty;
        
        [BsonElement("transactionId")]
        public string TransactionId { get; set; } = string.Empty;
        
        [BsonElement("data")]
        public LogData? Data { get; set; }
        
        [BsonElement("userId")]
        public string? UserId { get; set; }
        
        [BsonElement("ipAddress")]
        public string? IpAddress { get; set; }
        
        [BsonElement("requestUrl")]
        public string? RequestUrl { get; set; }
        
        [BsonElement("userAgent")]
        public string? UserAgent { get; set; }
        
        [BsonElement("errorMessage")]
        public string? ErrorMessage { get; set; }
        
        [BsonElement("stackTrace")]
        public string? StackTrace { get; set; }

        public SimpleLogEntry()
        {
            Id = ObjectId.GenerateNewId();
            Timestamp = DateTime.UtcNow.AddHours(8); // 台灣時區
        }
    }
}
