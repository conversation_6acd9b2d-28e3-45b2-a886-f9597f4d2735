using MongoDB.Driver;
using System;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// MongoDB 連接管理器介面
    /// </summary>
    public interface IMongoDBConnectionManager
    {
        Task<IMongoCollection<T>> GetCollectionAsync<T>(string collectionName);
        Task<bool> HealthCheckAsync();
        Task<bool> ResetConnectionAsync();
        Task<MongoDBConnectionStatus> GetConnectionStatusAsync();
    }

    /// <summary>
    /// MongoDB 連接狀態
    /// </summary>
    public class MongoDBConnectionStatus
    {
        public bool IsConnected { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public string DatabaseName { get; set; } = string.Empty;
        public DateTime LastHealthCheck { get; set; }
        public string? ErrorMessage { get; set; }
        public int ActiveConnections { get; set; }
    }
}
