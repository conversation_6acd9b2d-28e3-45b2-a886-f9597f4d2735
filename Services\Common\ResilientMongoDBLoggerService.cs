using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 彈性 MongoDB 日誌服務實作
    /// 具備重試機制、熔斷器保護和降級處理
    /// </summary>
    public class ResilientMongoDBLoggerService : IResilientLoggerService
    {
        private readonly IMongoDBConnectionManager _connectionManager;
        private readonly ILogDataProcessor _dataProcessor;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<ResilientMongoDBLoggerService> _logger;
        private readonly IConfiguration _configuration;
        
        // 熔斷器狀態
        private bool _circuitBreakerOpen = false;
        private DateTime _circuitBreakerOpenTime = DateTime.MinValue;
        private int _consecutiveFailures = 0;
        private readonly object _circuitBreakerLock = new object();
        
        // 統計資料
        private int _successCount = 0;
        private int _errorCount = 0;
        private DateTime _lastSuccessTime = DateTime.MinValue;
        private DateTime _lastErrorTime = DateTime.MinValue;
        private string? _lastErrorMessage;

        // 配置參數
        private readonly int _maxRetries;
        private readonly int _circuitBreakerThreshold;
        private readonly TimeSpan _circuitBreakerTimeout;
        private readonly string _collectionName;

        public ResilientMongoDBLoggerService(
            IMongoDBConnectionManager connectionManager,
            ILogDataProcessor dataProcessor,
            IHttpContextAccessor httpContextAccessor,
            ILogger<ResilientMongoDBLoggerService> logger,
            IConfiguration configuration)
        {
            _connectionManager = connectionManager;
            _dataProcessor = dataProcessor;
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _configuration = configuration;

            // 讀取配置
            _maxRetries = configuration.GetValue<int>("Logging:Resilient:RetryOptions:MaxRetries", 3);
            _circuitBreakerThreshold = configuration.GetValue<int>("Logging:Resilient:CircuitBreaker:FailureThreshold", 5);
            _circuitBreakerTimeout = TimeSpan.FromMilliseconds(
                configuration.GetValue<int>("Logging:Resilient:CircuitBreaker:TimeoutMs", 60000));
            _collectionName = configuration["MongoDB:CollectionName"] ?? "Logger_New";
        }

        #region ILoggerService Implementation

        public async Task LogDebugAsync(string message, string source = "System")
        {
            var entry = await _dataProcessor.ProcessLogEntryAsync(message, null, GenerateTransactionId(), source);
            entry.Level = "Debug";
            await TryLogAsync(entry);
        }

        public async Task LogInfoAsync(string message, string source = "System")
        {
            var entry = await _dataProcessor.ProcessLogEntryAsync(message, null, GenerateTransactionId(), source);
            entry.Level = "Information";
            await TryLogAsync(entry);
        }

        public async Task LogWarningAsync(string message, string source = "System")
        {
            var entry = await _dataProcessor.ProcessLogEntryAsync(message, null, GenerateTransactionId(), source);
            entry.Level = "Warning";
            await TryLogAsync(entry);
        }

        public async Task LogErrorAsync(string message, Exception? exception = null, string source = "System")
        {
            var entry = await _dataProcessor.ProcessLogEntryAsync(message, null, GenerateTransactionId(), source);
            entry.Level = "Error";
            entry.ErrorMessage = exception?.Message;
            entry.StackTrace = exception?.StackTrace;
            await TryLogAsync(entry);
        }

        public async Task LogDataAsync<T>(string message, T changedData, string transactionId, string source = "System") where T : class
        {
            var entry = await _dataProcessor.ProcessLogEntryAsync(message, changedData, transactionId, source);
            await TryLogAsync(entry);
        }

        #endregion

        #region IResilientLoggerService Implementation

        public async Task<bool> TryLogAsync(SimpleLogEntry entry, int maxRetries = 3)
        {
            // 檢查熔斷器狀態
            if (IsCircuitBreakerOpen())
            {
                _logger.LogWarning("熔斷器開啟，跳過日誌記錄");
                return false;
            }

            // 填充 HTTP 上下文資訊
            FillHttpContextInfo(entry);

            var actualMaxRetries = maxRetries > 0 ? maxRetries : _maxRetries;
            
            for (int attempt = 1; attempt <= actualMaxRetries; attempt++)
            {
                try
                {
                    var collection = await _connectionManager.GetCollectionAsync<SimpleLogEntry>(_collectionName);
                    await collection.InsertOneAsync(entry);
                    
                    // 記錄成功
                    RecordSuccess();
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "日誌記錄失敗 (嘗試 {Attempt}/{MaxRetries})", attempt, actualMaxRetries);
                    
                    if (attempt == actualMaxRetries)
                    {
                        // 最後一次嘗試失敗
                        RecordFailure(ex.Message);
                        return false;
                    }
                    
                    // 等待後重試
                    await Task.Delay(CalculateDelay(attempt));
                }
            }

            return false;
        }

        public async Task<LoggingHealthStatus> GetHealthStatusAsync()
        {
            var connectionStatus = await _connectionManager.GetConnectionStatusAsync();
            var totalRequests = _successCount + _errorCount;
            
            return new LoggingHealthStatus
            {
                IsHealthy = connectionStatus.IsConnected && !_circuitBreakerOpen,
                Status = GetStatusDescription(),
                SuccessCount = _successCount,
                ErrorCount = _errorCount,
                SuccessRate = totalRequests > 0 ? (double)_successCount / totalRequests * 100 : 100,
                CircuitBreakerOpen = _circuitBreakerOpen,
                LastSuccessTime = _lastSuccessTime,
                LastErrorTime = _lastErrorTime,
                LastErrorMessage = _lastErrorMessage
            };
        }

        public void EnableCircuitBreaker(bool enabled)
        {
            lock (_circuitBreakerLock)
            {
                if (!enabled)
                {
                    _circuitBreakerOpen = false;
                    _consecutiveFailures = 0;
                    _circuitBreakerOpenTime = DateTime.MinValue;
                }
            }
        }

        public async Task ResetCircuitBreakerAsync()
        {
            lock (_circuitBreakerLock)
            {
                _circuitBreakerOpen = false;
                _consecutiveFailures = 0;
                _circuitBreakerOpenTime = DateTime.MinValue;
            }
            
            _logger.LogInformation("熔斷器已重置");
            await Task.CompletedTask;
        }

        #endregion

        #region Private Methods

        private bool IsCircuitBreakerOpen()
        {
            lock (_circuitBreakerLock)
            {
                if (!_circuitBreakerOpen) return false;
                
                // 檢查是否可以嘗試恢復
                if (DateTime.UtcNow - _circuitBreakerOpenTime > _circuitBreakerTimeout)
                {
                    _circuitBreakerOpen = false;
                    _consecutiveFailures = 0;
                    _logger.LogInformation("熔斷器超時，嘗試恢復");
                    return false;
                }
                
                return true;
            }
        }

        private void RecordSuccess()
        {
            lock (_circuitBreakerLock)
            {
                Interlocked.Increment(ref _successCount);
                _lastSuccessTime = DateTime.UtcNow;
                _consecutiveFailures = 0;
                
                if (_circuitBreakerOpen)
                {
                    _circuitBreakerOpen = false;
                    _logger.LogInformation("熔斷器恢復正常");
                }
            }
        }

        private void RecordFailure(string errorMessage)
        {
            lock (_circuitBreakerLock)
            {
                Interlocked.Increment(ref _errorCount);
                _lastErrorTime = DateTime.UtcNow;
                _lastErrorMessage = errorMessage;
                _consecutiveFailures++;
                
                if (_consecutiveFailures >= _circuitBreakerThreshold && !_circuitBreakerOpen)
                {
                    _circuitBreakerOpen = true;
                    _circuitBreakerOpenTime = DateTime.UtcNow;
                    _logger.LogWarning("熔斷器開啟，連續失敗次數: {Failures}", _consecutiveFailures);
                }
            }
        }

        private TimeSpan CalculateDelay(int attempt)
        {
            // 指數退避算法
            var baseDelay = TimeSpan.FromMilliseconds(1000);
            var maxDelay = TimeSpan.FromMilliseconds(30000);
            var delay = TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, attempt - 1));
            
            return delay > maxDelay ? maxDelay : delay;
        }

        private void FillHttpContextInfo(SimpleLogEntry entry)
        {
            try
            {
                var context = _httpContextAccessor.HttpContext;
                if (context != null)
                {
                    entry.IpAddress = context.Connection.RemoteIpAddress?.ToString();
                    entry.RequestUrl = $"{context.Request.Scheme}://{context.Request.Host}{context.Request.Path}";
                    entry.UserAgent = context.Request.Headers["User-Agent"].ToString();
                    
                    // 嘗試從 Claims 中獲取用戶ID
                    if (context.User?.Identity?.IsAuthenticated == true)
                    {
                        entry.UserId = context.User.FindFirst("sub")?.Value ?? 
                                      context.User.FindFirst("id")?.Value ??
                                      context.User.Identity.Name;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "填充HTTP上下文資訊失敗");
            }
        }

        private string GetStatusDescription()
        {
            if (_circuitBreakerOpen) return "熔斷器開啟";
            if (_errorCount == 0) return "正常";
            
            var totalRequests = _successCount + _errorCount;
            var errorRate = (double)_errorCount / totalRequests * 100;
            
            return errorRate > 10 ? "不穩定" : "正常";
        }

        private string GenerateTransactionId()
        {
            return Guid.NewGuid().ToString();
        }

        #endregion
    }
}
