using FAST_ERP_Backend.Models.Common;
using System;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// 彈性日誌服務介面
    /// 提供重試機制、熔斷器模式和降級處理
    /// </summary>
    public interface IResilientLoggerService : ILoggerService
    {
        Task<bool> TryLogAsync(SimpleLogEntry entry, int maxRetries = 3);
        Task<LoggingHealthStatus> GetHealthStatusAsync();
        void EnableCircuitBreaker(bool enabled);
        Task ResetCircuitBreakerAsync();
    }

    /// <summary>
    /// 日誌系統健康狀態
    /// </summary>
    public class LoggingHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public int SuccessCount { get; set; }
        public int ErrorCount { get; set; }
        public double SuccessRate { get; set; }
        public bool CircuitBreakerOpen { get; set; }
        public DateTime LastSuccessTime { get; set; }
        public DateTime LastErrorTime { get; set; }
        public string? LastErrorMessage { get; set; }
    }
}
